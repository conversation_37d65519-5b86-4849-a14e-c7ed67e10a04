import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { ProductsService } from './products.service';
import { PrismaService } from '../prisma/prisma.service';
import { ProductResponseDto, ProductSummaryDto } from './dto';

describe('ProductsService', () => {
  let service: ProductsService;
  let prismaService: PrismaService;

  const mockProduct = {
    id: 'test-product-id',
    displayName: 'Test Product',
    tokenName: 'TEST',
    underlyingName: 'Test Underlying',
    symbol: 'TST',
    img: 'https://example.com/test.png',
    description: 'Test description',
    price: 100.50,
    high: 105.00,
    low: 95.00,
    priceChange24h: 5.50,
    priceChangePct24h: 5.75,
    apy: 8.25,
    tvl: 1000000.00,
    createdAt: new Date('2024-01-01T00:00:00.000Z'),
    updatedAt: new Date('2024-01-02T00:00:00.000Z'),
    supportedNetworks: [
      {
        id: 'network-1',
        networkName: 'Ethereum',
        networkCode: 'ETH',
        chainId: 1,
        address: '0x123...',
        decimals: 18,
        img: 'https://example.com/eth.png',
        createdAt: new Date('2024-01-01T00:00:00.000Z'),
        updatedAt: new Date('2024-01-01T00:00:00.000Z'),
      },
    ],
    productTags: [
      {
        tag: {
          slug: 'technology',
          label: 'Technology',
          category: {
            layer: '1',
            slug: 'asset-class',
            label: 'Asset Class',
          },
        },
      },
    ],
  };

  const mockPrismaService = {
    product: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ProductsService>(ProductsService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return array of ProductSummaryDto', async () => {
      const mockProducts = [mockProduct];
      mockPrismaService.product.findMany.mockResolvedValue(mockProducts);

      const result = await service.findAll();

      expect(result.status).toBe('success');
      expect(result.data).toHaveLength(1);
      expect(result.data[0]).toEqual({
        id: mockProduct.id,
        displayName: mockProduct.displayName,
        tokenName: mockProduct.tokenName,
        symbol: mockProduct.symbol,
        img: mockProduct.img,
        price: mockProduct.price,
        priceChange24h: mockProduct.priceChange24h,
        priceChangePct24h: mockProduct.priceChangePct24h,
        apy: mockProduct.apy,
        createdAt: mockProduct.createdAt.toISOString(),
      });

      // Verify that findMany was called without includes (optimized query)
      expect(mockPrismaService.product.findMany).toHaveBeenCalledWith({
        orderBy: {
          createdAt: 'desc',
        },
      });
    });

    it('should throw NotFoundException when no products found', async () => {
      mockPrismaService.product.findMany.mockResolvedValue([]);

      await expect(service.findAll()).rejects.toThrow(NotFoundException);
    });
  });

  describe('findOne', () => {
    it('should return detailed ProductResponseDto', async () => {
      mockPrismaService.product.findUnique.mockResolvedValue(mockProduct);

      const result = await service.findOne('test-product-id');

      expect(result.status).toBe('success');
      expect(result.data).toEqual({
        id: mockProduct.id,
        displayName: mockProduct.displayName,
        tokenName: mockProduct.tokenName,
        underlyingName: mockProduct.underlyingName,
        symbol: mockProduct.symbol,
        img: mockProduct.img,
        description: mockProduct.description,
        price: mockProduct.price,
        high: mockProduct.high,
        low: mockProduct.low,
        priceChange24h: mockProduct.priceChange24h,
        priceChangePct24h: mockProduct.priceChangePct24h,
        apy: mockProduct.apy,
        tvl: mockProduct.tvl,
        createdAt: mockProduct.createdAt.toISOString(),
        updatedAt: mockProduct.updatedAt.toISOString(),
        tags: [
          {
            categoryLayer: '1',
            categorySlug: 'asset-class',
            categoryLabel: 'Asset Class',
            tagSlug: 'technology',
            tagLabel: 'Technology',
          },
        ],
        supportedNetworks: [
          {
            id: 'network-1',
            networkName: 'Ethereum',
            networkCode: 'ETH',
            chainId: 1,
            address: '0x123...',
            decimals: 18,
            img: 'https://example.com/eth.png',
            createdAt: mockProduct.supportedNetworks[0].createdAt.toISOString(),
            updatedAt: mockProduct.supportedNetworks[0].updatedAt.toISOString(),
          },
        ],
      });

      // Verify that findUnique was called with includes (detailed query)
      expect(mockPrismaService.product.findUnique).toHaveBeenCalledWith({
        where: { id: 'test-product-id' },
        include: {
          supportedNetworks: true,
          productTags: {
            include: {
              tag: {
                include: {
                  category: true,
                },
              },
            },
            orderBy: {
              id: 'asc',
            },
          },
        },
      });
    });

    it('should throw NotFoundException when product not found', async () => {
      mockPrismaService.product.findUnique.mockResolvedValue(null);

      await expect(service.findOne('non-existent-id')).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});
