import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { PrismaModule } from '../prisma/prisma.module';
import { NavManagementModule } from '../backoffice/nav-management/nav-management.module';
import { TransactionStatusSchedulerService } from './services/transaction-status-scheduler.service';
import { JobMetricsService } from './services/job-metrics.service';
import { JobShutdownService } from './services/job-shutdown.service';

@Module({
  imports: [ScheduleModule.forRoot(), PrismaModule, NavManagementModule],
  providers: [
    TransactionStatusSchedulerService,
    JobMetricsService,
    JobShutdownService,
  ],
  exports: [
    TransactionStatusSchedulerService,
    JobMetricsService,
    JobShutdownService,
  ],
})
export class JobsModule {}
