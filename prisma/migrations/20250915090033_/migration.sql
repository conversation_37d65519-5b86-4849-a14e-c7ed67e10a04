-- CreateTable
CREATE TABLE "public"."product" (
    "id" UUID NOT NULL,
    "display_name" TEXT NOT NULL,
    "token_name" TEXT NOT NULL,
    "underlying_name" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "img" TEXT,
    "description" TEXT,
    "price" DECIMAL(18,8) NOT NULL,
    "high" DECIMAL(18,8),
    "low" DECIMAL(18,8),
    "price_change_24h" DECIMAL(18,8) NOT NULL,
    "price_change_pct_24h" DECIMAL(10,4) NOT NULL,
    "apy" DECIMAL(10,4),
    "tvl" DECIMAL(18,8),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."supported_network" (
    "id" UUID NOT NULL,
    "network_name" TEXT NOT NULL,
    "network_code" TEXT NOT NULL,
    "chain_id" INTEGER NOT NULL,
    "address" TEXT NOT NULL,
    "decimals" INTEGER NOT NULL,
    "product_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "supported_network_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "public"."supported_network" ADD CONSTRAINT "supported_network_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."product"("id") ON DELETE CASCADE ON UPDATE CASCADE;
