# fly.toml app configuration file generated for orbitum-app on 2025-09-01T14:02:08+07:00
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'orbitum-app'
primary_region = 'sin'

[build]

[deploy]
  release_command = "npx prisma migrate deploy"

[env]
  NODE_ENV = 'production'
  ENABLE_JOBS = 'true'
  PORT = '3000'
  HOST = '0.0.0.0'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1

[[vm]]
  memory = '512mb'
  cpu_kind = 'shared'
  cpus = 1

# Health check
[checks]
  [checks.health]
    grace_period = "60s"
    interval = "15s"
    method = "GET"
    path = "/api/v1/health"
    port = 3000
    timeout = "10s"
    type = "http"
    headers = { "User-Agent" = "fly-health-check" }
  
  # Startup probe for faster initial health checks
  [checks.startup]
    grace_period = "30s"
    interval = "5s"
    method = "GET"
    path = "/api/v1/ready"
    port = 3000
    timeout = "5s"
    type = "http"
    headers = { "User-Agent" = "fly-startup-check" }
