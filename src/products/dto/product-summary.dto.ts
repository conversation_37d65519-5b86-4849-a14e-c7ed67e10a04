import { ApiProperty } from '@nestjs/swagger';

export class ProductSummaryDto {
  @ApiProperty({ 
    description: 'Product ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({ 
    description: 'Product display name',
    example: 'Bitcoin ETF'
  })
  displayName: string;

  @ApiProperty({ 
    description: 'Token name',
    example: 'BTC-ETF'
  })
  tokenName: string;

  @ApiProperty({ 
    description: 'Product symbol',
    example: 'BTCETF'
  })
  symbol: string;

  @ApiProperty({
    description: 'Product image URL',
    required: false,
    example: 'https://orbitum.com/img/btc-etf.png',
  })
  img?: string;

  @ApiProperty({ 
    description: 'Current price',
    example: 45000.50
  })
  price: number;

  @ApiProperty({ 
    description: '24h price change',
    example: 1250.75
  })
  priceChange24h: number;

  @ApiProperty({ 
    description: '24h price change percentage',
    example: 2.85
  })
  priceChangePct24h: number;

  @ApiProperty({
    description: 'Annual Percentage Yield',
    required: false,
    example: 5.25,
  })
  apy?: number;

  @ApiProperty({ 
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  createdAt: string;
}
