import { ApiProperty } from '@nestjs/swagger';

export class ProductTagDto {
  @ApiProperty({ description: 'Category layer' })
  categoryLayer: string;

  @ApiProperty({ description: 'Category slug' })
  categorySlug: string;

  @ApiProperty({ description: 'Category label' })
  categoryLabel: string;

  @ApiProperty({ description: 'Tag slug' })
  tagSlug: string;

  @ApiProperty({ description: 'Tag label' })
  tagLabel: string;
}

export class SupportedNetworkDto {
  @ApiProperty({ description: 'Network ID' })
  id: string;

  @ApiProperty({ description: 'Network name' })
  networkName: string;

  @ApiProperty({ description: 'Network code' })
  networkCode: string;

  @ApiProperty({ description: 'Chain ID' })
  chainId: number;

  @ApiProperty({ description: 'Contract address' })
  address: string;

  @ApiProperty({ description: 'Token decimals' })
  decimals: number;

  @ApiProperty({
    description: 'Network image URL',
    required: false,
    example: 'https://example.com/network-logo.png',
  })
  img?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: string;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: string;
}

export class ProductResponseDto {
  @ApiProperty({ description: 'Product ID' })
  id: string;

  @ApiProperty({ description: 'Product display name' })
  displayName: string;

  @ApiProperty({ description: 'Token name' })
  tokenName: string;

  @ApiProperty({ description: 'Underlying asset name' })
  underlyingName: string;

  @ApiProperty({ description: 'Product symbol' })
  symbol: string;

  @ApiProperty({
    description: 'Product image URL',
    required: false,
    example: 'https://orbitum.com/img/orbitum.png',
  })
  img?: string;

  @ApiProperty({
    description: 'Product description',
    required: false,
    example:
      'A comprehensive investment product offering exposure to technology stocks',
  })
  description?: string;

  @ApiProperty({ description: 'Current price' })
  price: number;

  @ApiProperty({ description: '24h high price' })
  high: number;

  @ApiProperty({ description: '24h low price' })
  low: number;

  @ApiProperty({ description: '24h price change' })
  priceChange24h: number;

  @ApiProperty({ description: '24h price change percentage' })
  priceChangePct24h: number;

  @ApiProperty({
    description: 'Annual Percentage Yield',
    required: false,
    example: 5.25,
  })
  apy?: number;

  @ApiProperty({
    description: 'Total Value Locked',
    required: false,
    example: 1000000.5,
  })
  tvl?: number;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: string;

  @ApiProperty({
    description: 'Last update timestamp',
    required: false,
  })
  updatedAt?: string;

  @ApiProperty({ description: 'Product tags', type: [ProductTagDto] })
  tags: ProductTagDto[];

  @ApiProperty({
    description: 'Supported networks',
    type: [SupportedNetworkDto],
  })
  supportedNetworks: SupportedNetworkDto[];
}
