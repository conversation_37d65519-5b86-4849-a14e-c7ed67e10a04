-- Custom migration to convert UUID IDs to Integer IDs while preserving data
-- This migration handles: Category, Tag, and ProductTag tables

BEGIN;

-- Step 1: Add new integer ID columns
ALTER TABLE "public"."category" ADD COLUMN "new_id" SERIAL;
ALTER TABLE "public"."tag" ADD COLUMN "new_id" SERIAL;

-- Step 2: Add new integer foreign key columns
ALTER TABLE "public"."tag" ADD COLUMN "new_category_id" INTEGER;
ALTER TABLE "public"."product_tag" ADD COLUMN "new_tag_id" INTEGER;

-- Step 3: Create mapping tables to track UUID -> Integer relationships
CREATE TABLE "public"."category_id_mapping" (
    "old_uuid_id" UUID NOT NULL,
    "new_int_id" INTEGER NOT NULL,
    PRIMARY KEY ("old_uuid_id")
);

CREATE TABLE "public"."tag_id_mapping" (
    "old_uuid_id" UUID NOT NULL,
    "new_int_id" INTEGER NOT NULL,
    PRIMARY KEY ("old_uuid_id")
);

-- Step 4: Populate mapping tables
INSERT INTO "public"."category_id_mapping" ("old_uuid_id", "new_int_id")
SELECT "id", "new_id" FROM "public"."category";

INSERT INTO "public"."tag_id_mapping" ("old_uuid_id", "new_int_id")
SELECT "id", "new_id" FROM "public"."tag";

-- Step 5: Update foreign key references using the mappings
UPDATE "public"."tag" 
SET "new_category_id" = (
    SELECT "new_int_id" 
    FROM "public"."category_id_mapping" 
    WHERE "old_uuid_id" = "tag"."category_id"
);

UPDATE "public"."product_tag" 
SET "new_tag_id" = (
    SELECT "new_int_id" 
    FROM "public"."tag_id_mapping" 
    WHERE "old_uuid_id" = "product_tag"."tag_id"
);

-- Step 6: Drop old foreign key constraints
ALTER TABLE "public"."tag" DROP CONSTRAINT "tag_category_id_fkey";
ALTER TABLE "public"."product_tag" DROP CONSTRAINT "product_tag_tag_id_fkey";

-- Step 7: Drop old indexes that reference the UUID columns
DROP INDEX IF EXISTS "tag_category_id_idx";
DROP INDEX IF EXISTS "product_tag_tag_id_idx";

-- Step 8: Drop old UUID columns
ALTER TABLE "public"."category" DROP COLUMN "id";
ALTER TABLE "public"."tag" DROP COLUMN "id";
ALTER TABLE "public"."tag" DROP COLUMN "category_id";
ALTER TABLE "public"."product_tag" DROP COLUMN "tag_id";

-- Step 9: Rename new columns to replace old ones
ALTER TABLE "public"."category" RENAME COLUMN "new_id" TO "id";
ALTER TABLE "public"."tag" RENAME COLUMN "new_id" TO "id";
ALTER TABLE "public"."tag" RENAME COLUMN "new_category_id" TO "category_id";
ALTER TABLE "public"."product_tag" RENAME COLUMN "new_tag_id" TO "tag_id";

-- Step 10: Add primary key constraints
ALTER TABLE "public"."category" ADD CONSTRAINT "category_pkey" PRIMARY KEY ("id");
ALTER TABLE "public"."tag" ADD CONSTRAINT "tag_pkey" PRIMARY KEY ("id");

-- Step 11: Add NOT NULL constraints
ALTER TABLE "public"."tag" ALTER COLUMN "category_id" SET NOT NULL;
ALTER TABLE "public"."product_tag" ALTER COLUMN "tag_id" SET NOT NULL;

-- Step 12: Recreate foreign key constraints
ALTER TABLE "public"."tag" ADD CONSTRAINT "tag_category_id_fkey" 
    FOREIGN KEY ("category_id") REFERENCES "public"."category"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public"."product_tag" ADD CONSTRAINT "product_tag_tag_id_fkey" 
    FOREIGN KEY ("tag_id") REFERENCES "public"."tag"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Step 13: Recreate indexes
CREATE INDEX "tag_category_id_idx" ON "public"."tag"("category_id");
CREATE INDEX "product_tag_tag_id_idx" ON "public"."product_tag"("tag_id");

-- Step 14: Make Product.updatedAt nullable
ALTER TABLE "public"."product" ALTER COLUMN "updated_at" DROP NOT NULL;

-- Step 15: Clean up mapping tables
DROP TABLE "public"."category_id_mapping";
DROP TABLE "public"."tag_id_mapping";

COMMIT;
